import 'package:flutter/material.dart';
import '../../app_properties.dart';

class UsersManagementPage extends StatefulWidget {
  const UsersManagementPage({super.key});

  @override
  State<UsersManagementPage> createState() => _UsersManagementPageState();
}

class _UsersManagementPageState extends State<UsersManagementPage> {
  final List<Map<String, dynamic>> _users = [
    {
      'id': '1',
      'name': 'أحمد محمد',
      'email': '<EMAIL>',
      'phone': '+964 ************',
      'isActive': true,
      'joinDate': DateTime.now().subtract(const Duration(days: 30)),
      'ordersCount': 5,
    },
    {
      'id': '2',
      'name': 'فاطمة علي',
      'email': '<EMAIL>',
      'phone': '+964 ************',
      'isActive': true,
      'joinDate': DateTime.now().subtract(const Duration(days: 15)),
      'ordersCount': 3,
    },
    {
      'id': '3',
      'name': 'محمد حسن',
      'email': '<EMAIL>',
      'phone': '+964 ************',
      'isActive': false,
      'joinDate': DateTime.now().subtract(const Duration(days: 60)),
      'ordersCount': 1,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إدارة المستخدمين'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // إحصائيات سريعة
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي المستخدمين',
                    _users.length.toString(),
                    Icons.people,
                    AppColors.primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'المستخدمين النشطين',
                    _users.where((u) => u['isActive']).length.toString(),
                    Icons.check_circle,
                    AppColors.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // قائمة المستخدمين
            Expanded(
              child: ListView(
                children: _users.map((user) => _buildUserCard(user)).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(Map<String, dynamic> user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: user['isActive']
              ? AppColors.successColor.withValues(alpha: 0.1)
              : AppColors.errorColor.withValues(alpha: 0.1),
          child: Icon(
            Icons.person,
            color: user['isActive']
                ? AppColors.successColor
                : AppColors.errorColor,
          ),
        ),
        title: Text(
          user['name'],
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user['email']),
            Text(user['phone']),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: user['isActive']
                        ? AppColors.successColor.withValues(alpha: 0.1)
                        : AppColors.errorColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user['isActive'] ? 'نشط' : 'غير نشط',
                    style: TextStyle(
                      fontSize: 12,
                      color: user['isActive']
                          ? AppColors.successColor
                          : AppColors.errorColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${user['ordersCount']} طلبات',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility, size: 20),
                  SizedBox(width: 8),
                  Text('عرض التفاصيل'),
                ],
              ),
            ),
            PopupMenuItem(
              value: user['isActive'] ? 'deactivate' : 'activate',
              child: Row(
                children: [
                  Icon(
                    user['isActive'] ? Icons.block : Icons.check_circle,
                    size: 20,
                    color: user['isActive']
                        ? AppColors.errorColor
                        : AppColors.successColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    user['isActive'] ? 'إلغاء التفعيل' : 'تفعيل',
                    style: TextStyle(
                      color: user['isActive']
                          ? AppColors.errorColor
                          : AppColors.successColor,
                    ),
                  ),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: AppColors.errorColor),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: AppColors.errorColor)),
                ],
              ),
            ),
          ],
          onSelected: (value) {
            switch (value) {
              case 'view':
                _viewUserDetails(user);
                break;
              case 'activate':
              case 'deactivate':
                _toggleUserStatus(user);
                break;
              case 'delete':
                _deleteUser(user);
                break;
            }
          },
        ),
      ),
    );
  }

  void _viewUserDetails(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل ${user['name']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('البريد الإلكتروني: ${user['email']}'),
            const SizedBox(height: 8),
            Text('رقم الهاتف: ${user['phone']}'),
            const SizedBox(height: 8),
            Text('تاريخ الانضمام: ${_formatDate(user['joinDate'])}'),
            const SizedBox(height: 8),
            Text('عدد الطلبات: ${user['ordersCount']}'),
            const SizedBox(height: 8),
            Text('الحالة: ${user['isActive'] ? 'نشط' : 'غير نشط'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _toggleUserStatus(Map<String, dynamic> user) {
    setState(() {
      user['isActive'] = !user['isActive'];
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          user['isActive']
              ? 'تم تفعيل المستخدم بنجاح'
              : 'تم إلغاء تفعيل المستخدم بنجاح',
        ),
      ),
    );
  }

  void _deleteUser(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${user['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _users.remove(user);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حذف المستخدم بنجاح')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.errorColor),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
