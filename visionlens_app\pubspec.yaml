name: visionlens_app
description: "VisionLens - متجر إلكتروني للنظارات والعدسات اللاصقة"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI Components and Navigation
  cupertino_icons: ^1.0.8
  card_swiper: ^3.0.1
  flutter_staggered_grid_view: ^0.6.2
  rubber: ^1.0.1

  # Authentication and Security
  pin_code_text_field: ^1.8.0
  country_code_picker: ^3.0.0

  # Network and API
  http: ^1.1.0
  json_annotation: ^4.8.1

  # Rating and Interaction
  flutter_rating_bar: ^4.0.1
  numberpicker: ^2.1.2

  # Date and Time
  intl: ^0.18.1

  # Graphics and Icons
  flutter_svg: ^2.0.7

  # State Management and Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0

  # Image Handling
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4

  # Notifications
  flutter_local_notifications: ^16.3.0

  # Maps and Location
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # Firebase للتخزين السحابي والاستضافة
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_analytics: ^10.7.4
  google_sign_in: ^6.1.6

  # Network
  connectivity_plus: ^5.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  flutter_launcher_icons: ^0.13.1
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/

  # fonts will be added later
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
