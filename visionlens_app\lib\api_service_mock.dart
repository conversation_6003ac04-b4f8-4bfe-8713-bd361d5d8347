import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'models/product.dart';
import 'models/user_simple.dart';
import 'models/category.dart';
import 'models/order.dart';
import 'app_properties.dart';

class ApiService {
  static const String baseUrl = AppConstants.baseUrl;
  static const Duration timeoutDuration = Duration(seconds: 30);

  // حفظ التوكن في التخزين المحلي
  static Future<void> _saveAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userTokenKey, token);
  }

  // إزالة التوكن من التخزين المحلي
  static Future<void> _removeAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userTokenKey);
  }

  // ==================== خدمات المصادقة ====================

  // تسجيل الدخول
  static Future<AuthResponse> login(String email, String password) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 1));

      // محاكاة تسجيل الدخول
      if (email.isNotEmpty && password.isNotEmpty) {
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        await _saveAuthToken(token);

        final user = User(
          id: '1',
          email: email,
          firstName: 'مستخدم',
          lastName: 'VisionLens',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isEmailVerified: true,
        );

        return AuthResponse(user: user, token: token);
      } else {
        throw ApiException(
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'حدث خطأ في تسجيل الدخول');
    }
  }

  // التسجيل
  static Future<AuthResponse> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 2));

      // محاكاة التسجيل
      if (email.isNotEmpty &&
          password.isNotEmpty &&
          firstName.isNotEmpty &&
          lastName.isNotEmpty) {
        final token = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        await _saveAuthToken(token);

        final user = User(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          email: email,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isEmailVerified: true,
          isPhoneVerified: phone != null,
        );

        return AuthResponse(user: user, token: token);
      } else {
        throw ApiException(message: 'يرجى ملء جميع الحقول المطلوبة');
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'حدث خطأ في التسجيل');
    }
  }

  // إرسال رمز التحقق
  static Future<void> sendOTP(String phone) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة إرسال OTP
    } catch (e) {
      throw ApiException(message: 'فشل في إرسال رمز التحقق');
    }
  }

  // التحقق من رمز OTP
  static Future<void> verifyOTP(String phone, String otp) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة التحقق من OTP
      if (otp != '1234') {
        throw ApiException(message: 'رمز التحقق غير صحيح');
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(message: 'فشل في التحقق من الرمز');
    }
  }

  // نسيان كلمة المرور
  static Future<void> forgotPassword(String email) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      // محاكاة إرسال رابط إعادة تعيين كلمة المرور
    } catch (e) {
      throw ApiException(message: 'فشل في إرسال رابط إعادة التعيين');
    }
  }

  // تسجيل الخروج
  static Future<void> logout() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await _removeAuthToken();
    } catch (e) {
      // تجاهل الأخطاء في تسجيل الخروج
      await _removeAuthToken();
    }
  }

  // ==================== خدمات المنتجات ====================

  // جلب المنتجات
  static Future<List<Product>> getProducts({
    int page = 1,
    int limit = 20,
    String? categoryId,
    String? search,
    String? sortBy,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      await Future.delayed(const Duration(seconds: 1));

      // إنشاء منتجات تجريبية
      return _generateMockProducts(limit);
    } catch (e) {
      throw ApiException(message: 'فشل في جلب المنتجات');
    }
  }

  // جلب منتج بالمعرف
  static Future<Product> getProductById(String id) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      return _generateMockProducts(1).first;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب تفاصيل المنتج');
    }
  }

  // جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      await Future.delayed(const Duration(milliseconds: 800));

      return _generateMockProducts(5);
    } catch (e) {
      throw ApiException(message: 'فشل في جلب المنتجات المميزة');
    }
  }

  // ==================== خدمات الفئات ====================

  // جلب الفئات
  static Future<List<Category>> getCategories() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return _savedCategories;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب الفئات');
    }
  }

  // ==================== خدمات المستخدم ====================

  // جلب بيانات المستخدم الحالي
  static Future<User> getCurrentUser() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final user = User(
        id: '1',
        email: '<EMAIL>',
        firstName: 'مستخدم',
        lastName: 'VisionLens',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: true,
      );

      return user;
    } catch (e) {
      throw ApiException(message: 'فشل في جلب بيانات المستخدم');
    }
  }

  // تحديث بيانات المستخدم
  static Future<User> updateUser(Map<String, dynamic> userData) async {
    try {
      await Future.delayed(const Duration(seconds: 1));

      final user = User(
        id: userData['id'] ?? '1',
        email: userData['email'] ?? '<EMAIL>',
        firstName: userData['firstName'] ?? 'مستخدم',
        lastName: userData['lastName'] ?? 'VisionLens',
        phone: userData['phone'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: true,
      );

      return user;
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث بيانات المستخدم');
    }
  }

  // ==================== خدمات الفئات ====================

  // قائمة الفئات المحفوظة
  static final List<Category> _savedCategories = [
    Category(
      id: 'sunglasses',
      name: 'نظارات شمسية',
      description: 'نظارات شمسية عصرية وأنيقة لحماية عينيك من أشعة الشمس',
      image: 'assets/images/category_sunglasses.jpg',
      icon: 'wb_sunny',
      primaryColor: const Color(0xFFFF9800),
      secondaryColor: const Color(0xFFFFE0B2),
      productCount: 0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      id: 'eyeglasses',
      name: 'نظارات طبية',
      description: 'نظارات طبية عالية الجودة لتصحيح النظر',
      image: 'assets/images/category_eyeglasses.jpg',
      icon: 'visibility',
      primaryColor: const Color(0xFF1976D2),
      secondaryColor: const Color(0xFFBBDEFB),
      productCount: 0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      id: 'contact-lenses',
      name: 'عدسات لاصقة',
      description: 'عدسات لاصقة مريحة وآمنة للاستخدام اليومي',
      image: 'assets/images/category_contacts.jpg',
      icon: 'remove_red_eye',
      primaryColor: const Color(0xFF2196F3),
      secondaryColor: const Color(0xFFBBDEFB),
      productCount: 0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    Category(
      id: 'reading-glasses',
      name: 'نظارات قراءة',
      description: 'نظارات قراءة مريحة للقراءة والأعمال المكتبية',
      image: 'assets/images/category_reading.jpg',
      icon: 'menu_book',
      primaryColor: const Color(0xFF795548),
      secondaryColor: const Color(0xFFD7CCC8),
      productCount: 0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  // إضافة فئة جديدة
  static Future<void> addCategory(Category category) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _savedCategories.add(category);
    } catch (e) {
      throw ApiException(message: 'فشل في إضافة الفئة');
    }
  }

  // تحديث فئة
  static Future<void> updateCategory(Category category) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      final index = _savedCategories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _savedCategories[index] = category;
      }
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث الفئة');
    }
  }

  // حذف فئة
  static Future<void> deleteCategory(String categoryId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _savedCategories.removeWhere((c) => c.id == categoryId);
    } catch (e) {
      throw ApiException(message: 'فشل في حذف الفئة');
    }
  }

  // ==================== خدمات الطلبات ====================

  // جلب الطلبات
  static Future<List<Order>> getOrders() async {
    try {
      await Future.delayed(const Duration(milliseconds: 800));
      return _generateMockOrders();
    } catch (e) {
      throw ApiException(message: 'فشل في جلب الطلبات');
    }
  }

  // حذف منتج
  static Future<void> deleteProduct(String productId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      // محاكاة حذف المنتج
    } catch (e) {
      throw ApiException(message: 'فشل في حذف المنتج');
    }
  }

  // إنشاء طلبات تجريبية
  static List<Order> _generateMockOrders() {
    return [
      Order(
        id: 'order_1',
        date: DateTime.now().subtract(const Duration(hours: 2)),
        total: 150000.0,
        status: OrderStatus.processing,
        shippingAddress: 'بغداد، الكرادة، شارع الرئيسي',
        items: [
          const OrderItem(
            productId: 'product_1',
            productName: 'نظارة VisionLens 1',
            imageUrl: 'assets/images/product_1.jpg',
            quantity: 1,
            price: 75000.0,
          ),
          const OrderItem(
            productId: 'product_2',
            productName: 'نظارة VisionLens 2',
            imageUrl: 'assets/images/product_2.jpg',
            quantity: 1,
            price: 75000.0,
          ),
        ],
      ),
      Order(
        id: 'order_2',
        date: DateTime.now().subtract(const Duration(days: 1)),
        total: 100000.0,
        status: OrderStatus.confirmed,
        shippingAddress: 'البصرة، الزبير، حي الجامعة',
        items: [
          const OrderItem(
            productId: 'product_3',
            productName: 'عدسات لاصقة ملونة',
            imageUrl: 'assets/images/product_3.jpg',
            quantity: 2,
            price: 50000.0,
          ),
        ],
      ),
    ];
  }

  // قائمة المنتجات المحفوظة (فارغة في البداية)
  static final List<Product> _savedProducts = [];

  // إضافة منتج جديد
  static Future<void> addProduct(Product product) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _savedProducts.add(product);
    } catch (e) {
      throw ApiException(message: 'فشل في إضافة المنتج');
    }
  }

  // تحديث منتج
  static Future<void> updateProduct(Product product) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      final index = _savedProducts.indexWhere((p) => p.id == product.id);
      if (index != -1) {
        _savedProducts[index] = product;
      }
    } catch (e) {
      throw ApiException(message: 'فشل في تحديث المنتج');
    }
  }

  // إنشاء منتجات تجريبية (فارغة الآن)
  static List<Product> _generateMockProducts(int count) {
    // إرجاع المنتجات المحفوظة بدلاً من إنشاء منتجات تجريبية
    return _savedProducts;
  }
}

// استجابة المصادقة
class AuthResponse {
  final User user;
  final String token;

  AuthResponse({required this.user, required this.token});
}

// استثناء API
class ApiException implements Exception {
  final int? statusCode;
  final String message;

  ApiException({this.statusCode, required this.message});

  @override
  String toString() => message;
}
