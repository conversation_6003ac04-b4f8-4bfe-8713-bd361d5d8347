import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../../app_properties.dart';
import '../../services/auth_service_real_google.dart';
import '../../services/firebase_mock_service.dart';

/// صفحة اختبار Google Sign-In الحقيقي بحسابك الشخصي
class FirebaseTestPage extends StatefulWidget {
  const FirebaseTestPage({super.key});

  @override
  State<FirebaseTestPage> createState() => _FirebaseTestPageState();
}

class _FirebaseTestPageState extends State<FirebaseTestPage> {
  String _status = 'جاهز لاختبار Google Sign-In الحقيقي بحسابك الشخصي';
  bool _isLoading = false;
  GoogleSignInAccount? _googleUser;
  Map<String, dynamic> _stats = {};
  // Map<String, dynamic>? _userDetails; // غير مستخدم

  @override
  void initState() {
    super.initState();
    _updateUserInfo();
  }

  void _updateUserInfo() {
    setState(() {
      _googleUser = AuthService.currentGoogleUser;
      _stats = FirebaseMockService.getStats();
      // _userDetails = AuthService.currentUserDetails; // غير مستخدم
    });
  }

  Future<void> _testEmailLogin() async {
    setState(() {
      _isLoading = true;
      _status = 'اختبار تسجيل الدخول بالبريد الإلكتروني...';
    });

    try {
      final result = await AuthService.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
      );

      setState(() {
        _status = result.isSuccess
            ? 'تم تسجيل الدخول بنجاح: ${result.user?.email}'
            : 'فشل تسجيل الدخول: ${result.errorMessage}';
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateUserInfo();
    }
  }

  Future<void> _testGoogleLogin() async {
    setState(() {
      _isLoading = true;
      _status = 'اختبار تسجيل الدخول بـ Google...';
    });

    try {
      final result = await AuthService.signInWithGoogle();

      setState(() {
        _status = result.isSuccess
            ? 'تم تسجيل الدخول بـ Google بنجاح: ${result.user?.email}'
            : 'فشل تسجيل الدخول بـ Google: ${result.errorMessage}';
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateUserInfo();
    }
  }

  Future<void> _testCreateAccount() async {
    setState(() {
      _isLoading = true;
      _status = 'اختبار إنشاء حساب جديد...';
    });

    try {
      final result = await AuthService.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'مستخدم',
        lastName: 'جديد',
        phone: '+************',
      );

      setState(() {
        _status = result.isSuccess
            ? 'تم إنشاء الحساب بنجاح: ${result.user?.email}'
            : 'فشل إنشاء الحساب: ${result.errorMessage}';
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateUserInfo();
    }
  }

  Future<void> _testGoogleSignIn() async {
    setState(() {
      _isLoading = true;
      _status = 'اختبار Google Sign-In الحقيقي...';
    });

    try {
      // اختبار Google Sign-In الحقيقي
      final result = await AuthService.signInWithGoogle();

      setState(() {
        if (result.isSuccess && result.user != null) {
          _status = 'تم تسجيل الدخول بنجاح! مرحباً ${result.user!.firstName}';
        } else {
          _status = 'فشل تسجيل الدخول: ${result.errorMessage}';
        }
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ في تسجيل الدخول: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateUserInfo();
    }
  }

  Future<void> _testSignOut() async {
    setState(() {
      _isLoading = true;
      _status = 'اختبار تسجيل الخروج...';
    });

    try {
      final success = await AuthService.signOut();

      setState(() {
        _status = success ? 'تم تسجيل الخروج بنجاح' : 'فشل تسجيل الخروج';
      });
    } catch (e) {
      setState(() {
        _status = 'خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _updateUserInfo();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار Firebase المحاكي'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // حالة النظام
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'حالة النظام',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_status, style: const TextStyle(fontSize: 14)),
                    if (_isLoading) ...[
                      const SizedBox(height: 8),
                      const LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معلومات المستخدم الحالي
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات المستخدم الحالي',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_googleUser != null) ...[
                      Text('البريد الإلكتروني: ${_googleUser!.email}'),
                      Text('الاسم: ${_googleUser!.displayName ?? "غير محدد"}'),
                      Text('Google ID: ${_googleUser!.id}'),
                      if (_googleUser!.photoUrl != null)
                        const Text('صورة الملف الشخصي: متوفرة'),
                    ] else if (AuthService.currentUser != null) ...[
                      Text(
                        'البريد الإلكتروني: ${AuthService.currentUser!.email}',
                      ),
                      Text(
                        'الاسم: ${AuthService.currentUser!.firstName} ${AuthService.currentUser!.lastName}',
                      ),
                      Text(
                        'تم التحقق: ${AuthService.currentUser!.isEmailVerified ? "نعم" : "لا"}',
                      ),
                      Text('ID: ${AuthService.currentUser!.id}'),
                    ] else ...[
                      const Text('لا يوجد مستخدم مسجل حالياً'),
                    ],
                    const SizedBox(height: 8),
                    const Text('إحصائيات النظام:'),
                    Text('المستخدمين: ${_stats['users'] ?? 0}'),
                    Text('المنتجات: ${_stats['products'] ?? 0}'),
                    Text('الفئات: ${_stats['categories'] ?? 0}'),
                    Text('الطلبات: ${_stats['orders'] ?? 0}'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // أزرار الاختبار
            Expanded(
              child: ListView(
                children: [
                  _buildTestButton(
                    'تسجيل دخول بالبريد الإلكتروني',
                    _testEmailLogin,
                    Icons.email,
                  ),
                  _buildTestButton(
                    'تسجيل دخول بـ Google',
                    _testGoogleLogin,
                    Icons.login,
                  ),
                  _buildTestButton(
                    'إنشاء حساب جديد',
                    _testCreateAccount,
                    Icons.person_add,
                  ),
                  _buildTestButton(
                    'تسجيل دخول بـ Google الحقيقي',
                    _testGoogleSignIn,
                    Icons.login,
                  ),
                  _buildTestButton('تسجيل الخروج', _testSignOut, Icons.logout),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(String title, VoidCallback onPressed, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : onPressed,
        icon: Icon(icon),
        label: Text(title),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }
}
