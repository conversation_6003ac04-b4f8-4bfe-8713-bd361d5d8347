rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // قواعد صور المنتجات - قراءة للجميع، رفع للمدراء فقط
    match /products/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        (request.auth.token.admin == true || 
         request.auth.token.email in ['<EMAIL>']) &&
        request.resource.size < 5 * 1024 * 1024 && // حد أقصى 5 ميجابايت
        request.resource.contentType.matches('image/.*');
    }
    
    // قواعد صور المستخدمين - كل مستخدم يمكنه رفع صورته الشخصية
    match /users/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.uid == userId &&
        request.resource.size < 2 * 1024 * 1024 && // حد أ<PERSON>ى 2 ميجابايت
        request.resource.contentType.matches('image/.*');
    }
    
    // قواعد الملفات العامة - قراءة للجميع
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        (request.auth.token.admin == true || 
         request.auth.token.email in ['<EMAIL>']);
    }
  }
}
