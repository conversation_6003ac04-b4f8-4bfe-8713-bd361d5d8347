// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBvOiuK8QurMTLiTXyDnVfIQfMhA_dMcDg',
    appId: '1:123456789012:web:abcdef123456789012345678',
    messagingSenderId: '123456789012',
    projectId: 'visionlens-app-5ab70',
    authDomain: 'visionlens-app-5ab70.firebaseapp.com',
    storageBucket: 'visionlens-app-5ab70.appspot.com',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBvOiuK8QurMTLiTXyDnVfIQfMhA_dMcDg',
    appId: '1:123456789012:android:abcdef123456789012345678',
    messagingSenderId: '123456789012',
    projectId: 'visionlens-app-5ab70',
    storageBucket: 'visionlens-app-5ab70.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBvOiuK8QurMTLiTXyDnVfIQfMhA_dMcDg',
    appId: '1:123456789012:ios:abcdef123456789012345678',
    messagingSenderId: '123456789012',
    projectId: 'visionlens-app-5ab70',
    storageBucket: 'visionlens-app-5ab70.appspot.com',
    iosBundleId: 'com.example.visionlensApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBvOiuK8QurMTLiTXyDnVfIQfMhA_dMcDg',
    appId: '1:123456789012:macos:abcdef123456789012345678',
    messagingSenderId: '123456789012',
    projectId: 'visionlens-app-5ab70',
    storageBucket: 'visionlens-app-5ab70.appspot.com',
    iosBundleId: 'com.example.visionlensApp',
  );
}
