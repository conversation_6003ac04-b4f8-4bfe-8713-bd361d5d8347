rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المنتجات - قراءة للجميع، كتابة للمدراء فقط
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && 
        (request.auth.token.admin == true || 
         request.auth.token.email in ['<EMAIL>']);
    }
    
    // قواعد الطلبات - المستخدمون يمكنهم قراءة طلباتهم فقط
    match /orders/{orderId} {
      allow read: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         request.auth.token.admin == true ||
         request.auth.token.email in ['<EMAIL>']);
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && 
        (request.auth.token.admin == true ||
         request.auth.token.email in ['<EMAIL>']);
    }
    
    // قواعد المستخدمين - كل مستخدم يمكنه قراءة وتحديث بياناته فقط
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == userId || 
         request.auth.token.admin == true ||
         request.auth.token.email in ['<EMAIL>']);
    }
    
    // قواعد الفئات - قراءة للجميع، كتابة للمدراء فقط
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null && 
        (request.auth.token.admin == true || 
         request.auth.token.email in ['<EMAIL>']);
    }
    
    // قواعد التقييمات - المستخدمون يمكنهم إضافة تقييماتهم
    match /reviews/{reviewId} {
      allow read: if true;
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
      allow update, delete: if request.auth != null && 
        (resource.data.userId == request.auth.uid ||
         request.auth.token.admin == true ||
         request.auth.token.email in ['<EMAIL>']);
    }
    
    // قواعد الإحصائيات - للمدراء فقط
    match /analytics/{document=**} {
      allow read, write: if request.auth != null && 
        (request.auth.token.admin == true || 
         request.auth.token.email in ['<EMAIL>']);
    }
  }
}
