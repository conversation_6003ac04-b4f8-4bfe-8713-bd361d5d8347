import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product.dart';
import '../models/category.dart' as category_model;
import 'firebase_real_service.dart';

/// خدمة البيانات باستخدام Firestore - مشاركة البيانات بين جميع المستخدمين
class FirestoreDataService {
  static FirebaseFirestore? get _firestore => FirebaseRealService.firestore;

  // ==================== إدارة المنتجات ====================

  /// جلب قائمة المنتجات من Firestore
  static Future<List<Product>> getProducts() async {
    try {
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح، استخدام البيانات المحلية');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('products').get();
      final products = snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();

      if (kDebugMode) {
        print('📦 تم جلب ${products.length} منتج من Firestore');
      }

      return products;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المنتجات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة منتج جديد إلى Firestore
  static Future<void> addProduct(Product product) async {
    try {
      if (_firestore == null) {
        if (kDebugMode) print('⚠️ Firestore غير متاح');
        return;
      }

      await _firestore!
          .collection('products')
          .doc(product.id)
          .set(product.toJson());

      if (kDebugMode) {
        print('✅ تم إضافة المنتج إلى Firestore: ${product.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة المنتج إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث منتج في Firestore
  static Future<void> updateProduct(Product product) async {
    try {
      if (_firestore == null) {
        if (kDebugMode) print('⚠️ Firestore غير متاح');
        return;
      }

      await _firestore!
          .collection('products')
          .doc(product.id)
          .update(product.toJson());

      if (kDebugMode) {
        print('✅ تم تحديث المنتج في Firestore: ${product.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث المنتج في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف منتج من Firestore
  static Future<void> deleteProduct(String productId) async {
    try {
      if (_firestore == null) {
        if (kDebugMode) print('⚠️ Firestore غير متاح');
        return;
      }

      await _firestore!.collection('products').doc(productId).delete();

      if (kDebugMode) {
        print('✅ تم حذف المنتج من Firestore: $productId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف المنتج من Firestore: $e');
      }
      rethrow;
    }
  }

  /// البحث في المنتجات
  static Future<List<Product>> searchProducts(String query) async {
    final products = await getProducts();

    if (query.isEmpty) {
      return products;
    }

    return products.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase()) ||
          (product.brand?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  /// جلب المنتجات حسب الفئة
  static Future<List<Product>> getProductsByCategory(String categoryId) async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('categoryId', isEqualTo: categoryId)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المنتجات حسب الفئة: $e');
      }
      return [];
    }
  }

  /// جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      if (_firestore == null) return [];

      final snapshot = await _firestore!
          .collection('products')
          .where('isFeatured', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => Product.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب المنتجات المميزة: $e');
      }
      return [];
    }
  }

  // ==================== إدارة الفئات ====================

  /// جلب قائمة الفئات من Firestore
  static Future<List<category_model.Category>> getCategories() async {
    try {
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح، استخدام البيانات المحلية');
        }
        return [];
      }

      final snapshot = await _firestore!.collection('categories').get();
      final categories = snapshot.docs
          .map(
            (doc) =>
                category_model.Category.fromJson({...doc.data(), 'id': doc.id}),
          )
          .toList();

      if (kDebugMode) {
        print('📂 تم جلب ${categories.length} فئة من Firestore');
      }

      return categories;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في جلب الفئات من Firestore: $e');
      }
      return [];
    }
  }

  /// إضافة فئة جديدة إلى Firestore
  static Future<void> addCategory(category_model.Category category) async {
    try {
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('categories')
          .doc(category.id)
          .set(category.toJson());

      if (kDebugMode) {
        print('✅ تم إضافة الفئة إلى Firestore: ${category.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة الفئة إلى Firestore: $e');
      }
      rethrow;
    }
  }

  /// تحديث فئة في Firestore
  static Future<void> updateCategory(category_model.Category category) async {
    try {
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!
          .collection('categories')
          .doc(category.id)
          .update(category.toJson());

      if (kDebugMode) {
        print('✅ تم تحديث الفئة في Firestore: ${category.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث الفئة في Firestore: $e');
      }
      rethrow;
    }
  }

  /// حذف فئة من Firestore
  static Future<void> deleteCategory(String categoryId) async {
    try {
      if (_firestore == null) {
        if (kDebugMode) {
          print('⚠️ Firestore غير متاح');
        }
        return;
      }

      await _firestore!.collection('categories').doc(categoryId).delete();

      if (kDebugMode) {
        print('✅ تم حذف الفئة من Firestore: $categoryId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف الفئة من Firestore: $e');
      }
      rethrow;
    }
  }

  // ==================== مزامنة البيانات ====================

  /// مزامنة البيانات المحلية مع Firestore
  static Future<void> syncLocalDataToFirestore() async {
    try {
      if (_firestore == null) {
        if (kDebugMode) print('⚠️ Firestore غير متاح للمزامنة');
        return;
      }

      if (kDebugMode) {
        print('🔄 بدء مزامنة البيانات المحلية مع Firestore...');
      }

      // مزامنة المنتجات المحلية
      // يمكن إضافة هذا لاحقاً إذا احتجنا

      if (kDebugMode) {
        print('✅ تمت مزامنة البيانات مع Firestore');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في مزامنة البيانات: $e');
      }
    }
  }

  /// التحقق من اتصال Firestore
  static Future<bool> isFirestoreAvailable() async {
    try {
      if (_firestore == null) return false;
      await _firestore!.collection('test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }
}
