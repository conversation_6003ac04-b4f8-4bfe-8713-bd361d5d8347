<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="VisionLens - متجر النظارات والعدسات اللاصقة">

  <!-- Google Sign-In Configuration -->
  <!-- ⚠️ استبدل CLIENT_ID بالقيمة الحقيقية من Google Cloud Console -->
  <meta name="google-signin-client_id" content="627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="VisionLens">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>VisionLens</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #2196f3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #2196f3;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل VisionLens...</div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Firebase Configuration -->
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw",
      authDomain: "visionlens-app-5ab70.firebaseapp.com",
      projectId: "visionlens-app-5ab70",
      storageBucket: "visionlens-app-5ab70.appspot.com",
      messagingSenderId: "************",
      appId: "1:************:web:a7cb9386ef6d04c7fe67ec"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    const db = firebase.firestore();

    console.log('🔥 Firebase initialized successfully');

    // دوال Firebase للاستخدام من Flutter
    window.firebaseHelpers = {
      // متغير لحفظ المنتجات
      cachedProducts: [],
      isLoading: false,

      // جلب المنتجات مع Promise صحيح
      async getProductsAsync() {
        if (this.isLoading) {
          console.log('⏳ JavaScript: جلب المنتجات قيد التنفيذ...');
          return this.cachedProducts;
        }

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب المنتجات من Firestore...');
          const snapshot = await db.collection('products').get();
          const products = [];
          snapshot.forEach(doc => {
            products.push({ id: doc.id, ...doc.data() });
          });
          console.log(`📦 JavaScript: تم جلب ${products.length} منتج`);

          // حفظ المنتجات في المتغير العام
          this.cachedProducts = products;
          window.latestProducts = products;

          console.log(`💾 JavaScript: تم حفظ ${this.cachedProducts.length} منتج في Cache`);

          return products;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المنتجات:', error);
          this.cachedProducts = [];
          return [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب المنتجات (النسخة القديمة للتوافق)
      async getProducts() {
        return await this.getProductsAsync();
      },

      // جلب المنتجات المحفوظة
      getCachedProducts() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedProducts.length} منتج محفوظ`);
        return this.cachedProducts;
      },

      // إضافة منتج
      async addProduct(productData) {
        try {
          console.log('🔄 JavaScript: إضافة منتج:', productData.name);
          await db.collection('products').doc(productData.id).set(productData);
          console.log('✅ JavaScript: تم إضافة المنتج بنجاح:', productData.name);

          // تحديث Cache
          await this.getProductsAsync();
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة المنتج:', error);
          return { success: false, error: error.message };
        }
      },

      // تعديل منتج
      async updateProduct(productData) {
        try {
          console.log('🔄 JavaScript: تعديل منتج:', productData.name);
          console.log('📝 JavaScript: بيانات التعديل:', productData);

          const result = await db.collection('products').doc(productData.id).update(productData);
          console.log('✅ JavaScript: تم تعديل المنتج بنجاح:', productData.name);
          console.log('📊 JavaScript: نتيجة التعديل:', result);

          // تحديث Cache
          await this.getProductsAsync();
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تعديل المنتج:', error);
          console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
          return { success: false, error: error.message };
        }
      },

      // حذف منتج مع Promise صحيح
      deleteProduct(productId) {
        console.log('🔄 JavaScript: بدء حذف منتج:', productId);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار المنتج:', `products/${productId}`);

            // التحقق من وجود المنتج أولاً
            const docRef = db.collection('products').doc(productId);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: المنتج غير موجود:', productId);
              resolve({ success: false, error: 'المنتج غير موجود' });
              return;
            }

            console.log('📋 JavaScript: المنتج موجود، بدء الحذف...');
            await docRef.delete();
            console.log('✅ JavaScript: تم حذف المنتج بنجاح من Firebase:', productId);

            // تحديث Cache
            await this.getProductsAsync();
            console.log('� JavaScript: تم تحديث Cache بعد الحذف');

            // التحقق من الحذف
            const checkSnap = await docRef.get();
            if (!checkSnap.exists) {
              console.log('✅ JavaScript: تأكيد الحذف - المنتج لم يعد موجود');
              resolve({ success: true });
            } else {
              console.log('❌ JavaScript: فشل الحذف - المنتج ما زال موجود');
              resolve({ success: false, error: 'فشل في الحذف' });
            }

          } catch (error) {
            console.error('❌ JavaScript: خطأ في حذف المنتج:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // === إدارة الفئات ===
      // جلب الفئات
      async getCategories() {
        try {
          console.log('🔄 JavaScript: جلب الفئات من Firestore...');
          const snapshot = await db.collection('categories').get();
          const categories = [];
          snapshot.forEach(doc => {
            categories.push({ id: doc.id, ...doc.data() });
          });
          console.log(`📦 JavaScript: تم جلب ${categories.length} فئة`);
          return categories;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الفئات:', error);
          return [];
        }
      },

      // إضافة فئة
      async addCategory(categoryData) {
        try {
          console.log('🔄 JavaScript: إضافة فئة:', categoryData.name);
          await db.collection('categories').doc(categoryData.id).set(categoryData);
          console.log('✅ JavaScript: تم إضافة الفئة بنجاح:', categoryData.name);
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة الفئة:', error);
          return { success: false, error: error.message };
        }
      },

      // تعديل فئة
      async updateCategory(categoryData) {
        try {
          console.log('🔄 JavaScript: تعديل فئة:', categoryData.name);
          await db.collection('categories').doc(categoryData.id).update(categoryData);
          console.log('✅ JavaScript: تم تعديل الفئة بنجاح:', categoryData.name);
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تعديل الفئة:', error);
          return { success: false, error: error.message };
        }
      },

      // حذف فئة
      async deleteCategory(categoryId) {
        try {
          console.log('🔄 JavaScript: حذف فئة:', categoryId);
          await db.collection('categories').doc(categoryId).delete();
          console.log('✅ JavaScript: تم حذف الفئة بنجاح:', categoryId);
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في حذف الفئة:', error);
          return { success: false, error: error.message };
        }
      },

      // === إدارة المستخدمين ===
      // جلب المستخدمين
      async getUsers() {
        try {
          console.log('🔄 JavaScript: جلب المستخدمين من Firestore...');
          const snapshot = await db.collection('users').get();
          const users = [];
          snapshot.forEach(doc => {
            users.push({ id: doc.id, ...doc.data() });
          });
          console.log(`📦 JavaScript: تم جلب ${users.length} مستخدم`);
          return users;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المستخدمين:', error);
          return [];
        }
      },

      // === إدارة الطلبات ===
      // جلب الطلبات
      async getOrders() {
        try {
          console.log('🔄 JavaScript: جلب الطلبات من Firestore...');
          const snapshot = await db.collection('orders').get();
          const orders = [];
          snapshot.forEach(doc => {
            orders.push({ id: doc.id, ...doc.data() });
          });
          console.log(`📦 JavaScript: تم جلب ${orders.length} طلب`);
          return orders;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الطلبات:', error);
          return [];
        }
      },

      // تحديث حالة الطلب
      async updateOrderStatus(orderId, status) {
        try {
          console.log('🔄 JavaScript: تحديث حالة الطلب:', orderId, 'إلى:', status);
          await db.collection('orders').doc(orderId).update({ status: status });
          console.log('✅ JavaScript: تم تحديث حالة الطلب بنجاح');
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تحديث حالة الطلب:', error);
          return { success: false, error: error.message };
        }
      },

      // تسجيل الدخول بـ Google
      async signInWithGoogle() {
        try {
          console.log('🔄 JavaScript: تسجيل الدخول بـ Google...');
          const provider = new firebase.auth.GoogleAuthProvider();
          const result = await firebase.auth().signInWithPopup(provider);
          const user = result.user;
          console.log('✅ JavaScript: تم تسجيل الدخول بنجاح');
          return {
            success: true,
            user: {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
              photoURL: user.photoURL
            }
          };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تسجيل الدخول:', error);
          return { success: false, error: error.message };
        }
      },

      // تسجيل الخروج
      async signOut() {
        try {
          await firebase.auth().signOut();
          console.log('✅ JavaScript: تم تسجيل الخروج');
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تسجيل الخروج:', error);
          return { success: false, error: error.message };
        }
      }
    };
  </script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
