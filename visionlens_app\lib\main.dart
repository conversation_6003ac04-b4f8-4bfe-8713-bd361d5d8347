import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'app_properties.dart';
import 'screens/splash_page.dart';
import 'services/firebase_mock_service.dart';
// import 'services/firebase_real_service.dart'; // معطل مؤقت<|im_start|>

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // استخدام Firebase Mock Service (مؤقت<|im_start|>)
    await FirebaseMockService.initialize();
    if (kDebugMode) {
      print('✅ Firebase Mock Service المحسن تم تهيئته بنجاح');
      print('🔥 يدعم Google Sign-In الحقيقي!');
      print('🌐 جاهز للنشر على Firebase Hosting!');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ خطأ في تهيئة Firebase Mock Service: $e');
    }
  }

  runApp(const VisionLensApp());
}

class VisionLensApp extends StatelessWidget {
  const VisionLensApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تعيين اتجاه النص للعربية
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,

      // دعم اللغة العربية
      locale: const Locale('ar', 'SA'),

      // تعيين اتجاه النص من اليمين لليسار
      builder: (context, child) {
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },

      home: const SplashPage(),
    );
  }
}
