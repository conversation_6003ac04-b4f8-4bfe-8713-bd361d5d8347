import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'app_properties.dart';
import 'screens/splash_page.dart';
import 'services/firebase_mock_service.dart';
import 'services/firebase_real_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // محاولة تهيئة Firebase الحقيقي أولاً
    await FirebaseRealService.initialize();
    if (kDebugMode) {
      print('✅ Firebase الحقيقي تم تهيئته بنجاح');
      print('🔥 يدعم التخزين السحابي والمحلي!');
      print('🌐 جاهز للاستضافة على Firebase Hosting!');

      // تحقق من حالة Firestore
      final firestoreInstance = FirebaseRealService.firestore;
      if (firestoreInstance != null) {
        print('✅ Firestore متاح ومتصل');
        print(
          '🔗 رابط المشروع: https://console.firebase.google.com/project/visionlens-app-5ab70/firestore',
        );
      } else {
        print('❌ Firestore غير متاح');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ خطأ في تهيئة Firebase الحقيقي: $e');
      print('🔄 التبديل للخدمة المحاكية...');
    }

    // في حالة فشل Firebase الحقيقي، استخدم المحاكي
    try {
      await FirebaseMockService.initialize();
      if (kDebugMode) {
        print('✅ Firebase Mock Service المحسن تم تهيئته بنجاح');
        print('🔥 يدعم Google Sign-In الحقيقي!');
      }
    } catch (e2) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة Firebase Mock Service: $e2');
      }
    }
  }

  runApp(const VisionLensApp());
}

class VisionLensApp extends StatelessWidget {
  const VisionLensApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تعيين اتجاه النص للعربية
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,

      // دعم اللغة العربية
      locale: const Locale('ar', 'SA'),

      // تعيين اتجاه النص من اليمين لليسار
      builder: (context, child) {
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },

      home: const SplashPage(),
    );
  }
}
