import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_simple.dart' as user_model;
import 'hybrid_storage_service.dart';

/// خدمة Firebase الحقيقية مع دعم التخزين الهجين
class FirebaseRealService {
  static FirebaseAuth? _auth;
  static FirebaseFirestore? _firestore;
  static GoogleSignIn? _googleSignIn;
  static bool _isInitialized = false;

  /// تهيئة Firebase
  static Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      if (kDebugMode) print('🔥 بدء تهيئة Firebase الحقيقي...');

      // تهيئة Firebase Core
      await Firebase.initializeApp(
        options: const FirebaseOptions(
          apiKey: "AIzaSyBvOiuK8QurMTLiTXyDnVfIQfMhA_dMcDg",
          authDomain: "visionlens-app.firebaseapp.com",
          projectId: "visionlens-app",
          storageBucket: "visionlens-app.appspot.com",
          messagingSenderId: "123456789012",
          appId: "1:123456789012:web:abcdef123456789012345678",
        ),
      );

      // تهيئة الخدمات
      _auth = FirebaseAuth.instance;
      _firestore = FirebaseFirestore.instance;
      
      // تهيئة Google Sign-In
      _googleSignIn = GoogleSignIn(
        clientId: kIsWeb 
          ? '123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com'
          : null,
        scopes: [
          'email',
          'profile',
          'openid',
          'https://www.googleapis.com/auth/userinfo.email',
          'https://www.googleapis.com/auth/userinfo.profile',
        ],
      );

      // تهيئة خدمة التخزين الهجين
      await HybridStorageService.initialize();

      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ تم تهيئة Firebase الحقيقي بنجاح');
        print('🔥 يدعم التخزين السحابي والمحلي!');
        print('🌐 جاهز للاستضافة على Firebase Hosting!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة Firebase: $e');
        print('🔄 التبديل للوضع المحلي...');
      }
      // في حالة فشل Firebase، استخدم الخدمة المحاكية
      _isInitialized = true;
    }
  }

  /// تسجيل الدخول بـ Google
  static Future<user_model.User?> signInWithGoogle() async {
    try {
      if (kDebugMode) print('🔄 بدء تسجيل الدخول بـ Google الحقيقي...');
      
      if (!_isInitialized) await initialize();

      // تسجيل الدخول بـ Google
      final GoogleSignInAccount? googleUser = await _googleSignIn?.signIn();
      if (googleUser == null) {
        if (kDebugMode) print('❌ تم إلغاء تسجيل الدخول');
        return null;
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // إنشاء بيانات اعتماد Firebase
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // تسجيل الدخول في Firebase
      UserCredential? userCredential;
      if (_auth != null) {
        userCredential = await _auth!.signInWithCredential(credential);
      }

      // إنشاء نموذج المستخدم
      final user = user_model.User(
        id: userCredential?.user?.uid ?? googleUser.id,
        name: googleUser.displayName ?? 'مستخدم',
        email: googleUser.email,
        photoUrl: googleUser.photoUrl,
        isAdmin: googleUser.email == '<EMAIL>',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        googleId: googleUser.id,
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
        platform: kIsWeb ? 'web' : 'mobile',
        signInMethod: 'google',
        signInTime: DateTime.now(),
        userAgent: kIsWeb ? 'Chrome/Web' : 'Mobile App',
      );

      // حفظ المستخدم في التخزين الهجين
      await HybridStorageService.addUser(user.toJson());

      // حفظ في SharedPreferences للجلسة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_token', googleAuth.accessToken ?? '');
      await prefs.setString('user_data', jsonEncode(user.toJson()));

      if (kDebugMode) {
        print('✅ تم تسجيل الدخول بنجاح!');
        print('📧 البريد الإلكتروني: ${user.email}');
        print('👤 الاسم: ${user.name}');
        print('🆔 Firebase UID: ${userCredential?.user?.uid}');
        print('🆔 Google ID: ${user.googleId}');
        print('🔑 تم الحصول على رموز المصادقة');
        print('🎫 Access Token: ${googleAuth.accessToken != null ? 'متوفر' : 'غير متوفر'}');
        print('🎟️ ID Token: ${googleAuth.idToken != null ? 'متوفر' : 'غير متوفر'}');
        print('✅ تم حفظ المستخدم في Firebase والتخزين المحلي');
        print('🎉 مرحباً ${user.name}!');
      }

      return user;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تسجيل الدخول: $e');
      return null;
    }
  }

  /// تسجيل الخروج
  static Future<void> signOut() async {
    try {
      if (kDebugMode) print('🔄 تسجيل الخروج...');

      // تسجيل الخروج من Firebase
      if (_auth != null) {
        await _auth!.signOut();
      }

      // تسجيل الخروج من Google
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }

      // مسح البيانات المحلية
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_token');
      await prefs.remove('user_data');

      if (kDebugMode) print('✅ تم تسجيل الخروج بنجاح');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تسجيل الخروج: $e');
    }
  }

  /// الحصول على المستخدم الحالي
  static User? getCurrentUser() {
    return _auth?.currentUser;
  }

  /// التحقق من حالة تسجيل الدخول
  static bool isSignedIn() {
    return _auth?.currentUser != null;
  }

  /// الحصول على Firestore instance
  static FirebaseFirestore? get firestore => _firestore;

  /// الحصول على Auth instance
  static FirebaseAuth? get auth => _auth;

  /// مزامنة البيانات مع السحابة
  static Future<void> syncData() async {
    try {
      if (kDebugMode) print('🔄 مزامنة البيانات مع Firebase...');
      await HybridStorageService.manualSync();
      if (kDebugMode) print('✅ تمت المزامنة بنجاح');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في المزامنة: $e');
    }
  }

  /// التحقق من حالة الاتصال بالسحابة
  static Future<bool> isCloudConnected() async {
    return await HybridStorageService.isCloudConnected();
  }

  /// الحصول على آخر وقت مزامنة
  static Future<DateTime?> getLastSyncTime() async {
    return await HybridStorageService.getLastSyncTime();
  }

  /// إضافة منتج للسحابة والتخزين المحلي
  static Future<void> addProduct(Map<String, dynamic> productData) async {
    await HybridStorageService.addProduct(productData);
  }

  /// إضافة طلب للسحابة والتخزين المحلي
  static Future<void> addOrder(Map<String, dynamic> orderData) async {
    await HybridStorageService.addOrder(orderData);
  }

  /// تحديث طلب في السحابة والتخزين المحلي
  static Future<void> updateOrder(Map<String, dynamic> orderData) async {
    await HybridStorageService.updateOrder(orderData);
  }

  /// إضافة مستخدم للسحابة والتخزين المحلي
  static Future<void> addUser(Map<String, dynamic> userData) async {
    await HybridStorageService.addUser(userData);
  }
}
