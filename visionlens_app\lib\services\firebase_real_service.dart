import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_simple.dart' as user_model;
import '../firebase_options.dart';

/// خدمة Firebase الحقيقية - مُفعّلة
class FirebaseRealService {
  static FirebaseAuth? _auth;
  static FirebaseFirestore? _firestore;
  static GoogleSignIn? _googleSignIn;
  static bool _isInitialized = false;

  /// طباعة رسالة في Console للويب والموبايل
  static void _logMessage(String message) {
    print('🔥 Firebase: $message');
    if (kDebugMode) {
      debugPrint('🔥 Firebase: $message');
    }
  }

  /// تهيئة Firebase
  static Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      _logMessage('🔥 بدء تهيئة Firebase الحقيقي...');

      // تهيئة Firebase Core مع التكوين الصحيح
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      // تهيئة الخدمات
      _auth = FirebaseAuth.instance;
      _firestore = FirebaseFirestore.instance;
      _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

      // انتظار قليل للتأكد من اكتمال التهيئة
      await Future.delayed(const Duration(milliseconds: 500));

      _isInitialized = true;

      _logMessage('✅ تم تهيئة Firebase الحقيقي بنجاح');
      _logMessage('🔥 Firebase Auth: ${_auth != null ? 'متاح' : 'غير متاح'}');
      _logMessage('🔥 Firestore: ${_firestore != null ? 'متاح' : 'غير متاح'}');
      _logMessage(
        '🔥 Google Sign-In: ${_googleSignIn != null ? 'متاح' : 'غير متاح'}',
      );
      _logMessage('🆔 Project ID: visionlens-app-5ab70');
      _logMessage('🌐 Auth Domain: visionlens-app-5ab70.firebaseapp.com');
      _logMessage(
        '🔗 Firestore URL: https://console.firebase.google.com/project/visionlens-app-5ab70/firestore',
      );

      // اختبار الاتصال بـ Firestore
      try {
        await _firestore!.collection('test').limit(1).get();
        _logMessage('✅ اختبار الاتصال بـ Firestore نجح');
      } catch (e) {
        _logMessage('❌ فشل اختبار الاتصال بـ Firestore: $e');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تهيئة Firebase: $e');
      }
      rethrow;
    }
  }

  /// تسجيل الدخول بـ Google
  static Future<user_model.User?> signInWithGoogle() async {
    try {
      if (kDebugMode) {
        print('🔄 بدء تسجيل الدخول بـ Google...');
      }

      if (!_isInitialized) await initialize();

      if (_googleSignIn == null || _auth == null) {
        throw Exception('Firebase غير مهيأ');
      }

      // تسجيل الدخول بـ Google
      final GoogleSignInAccount? googleUser = await _googleSignIn!.signIn();
      if (googleUser == null) {
        if (kDebugMode) {
          print('❌ تم إلغاء تسجيل الدخول بـ Google');
        }
        return null;
      }

      // الحصول على بيانات المصادقة
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // إنشاء بيانات اعتماد Firebase
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // تسجيل الدخول في Firebase
      final UserCredential userCredential = await _auth!.signInWithCredential(
        credential,
      );

      if (userCredential.user != null) {
        // تقسيم الاسم الكامل
        final fullName = googleUser.displayName ?? 'مستخدم';
        final nameParts = fullName.split(' ');
        final firstName = nameParts.isNotEmpty ? nameParts.first : 'مستخدم';
        final lastName = nameParts.length > 1
            ? nameParts.sublist(1).join(' ')
            : 'جديد';

        // إنشاء نموذج المستخدم
        final user = user_model.User(
          id: userCredential.user!.uid,
          email: userCredential.user!.email ?? googleUser.email,
          firstName: firstName,
          lastName: lastName,
          profileImage: userCredential.user!.photoURL ?? googleUser.photoUrl,
          isEmailVerified: userCredential.user!.emailVerified,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // حفظ المستخدم في Firestore
        await _firestore!
            .collection('users')
            .doc(user.id)
            .set(user.toJson(), SetOptions(merge: true));

        // حفظ بيانات تسجيل الدخول
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('current_user', jsonEncode(user.toJson()));
        await prefs.setBool('is_logged_in', true);

        if (kDebugMode) {
          print('✅ تم تسجيل الدخول بنجاح!');
          print('📧 البريد الإلكتروني: ${user.email}');
          print('👤 الاسم: ${user.fullName}');
          print('🆔 Firebase UID: ${userCredential.user?.uid}');
          print('🆔 Google ID: ${googleUser.id}');
        }

        return user;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الدخول بـ Google: $e');
      }
      return null;
    }
  }

  /// تسجيل الخروج
  static Future<void> signOut() async {
    try {
      if (_auth != null) {
        await _auth!.signOut();
      }
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }

      // مسح بيانات تسجيل الدخول
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      await prefs.setBool('is_logged_in', false);

      if (kDebugMode) {
        print('✅ تم تسجيل الخروج بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تسجيل الخروج: $e');
      }
    }
  }

  /// الحصول على المستخدم الحالي
  static User? getCurrentUser() {
    return _auth?.currentUser;
  }

  /// التحقق من حالة تسجيل الدخول
  static bool isSignedIn() {
    return _auth?.currentUser != null;
  }

  /// الحصول على Firestore instance
  static FirebaseFirestore? get firestore => _firestore;

  /// الحصول على Auth instance
  static FirebaseAuth? get auth => _auth;
}
