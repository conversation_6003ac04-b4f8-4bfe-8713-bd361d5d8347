// ملف معطل مؤقت<|im_start|> - سيتم تفعيله بعد حل مشاكل Firebase
/*
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'data_service.dart';

/// خدمة التخزين الهجين - تدعم التخزين السحابي والمحلي
class HybridStorageService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _lastSyncKey = 'last_sync_timestamp';

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      if (kDebugMode) print('🔄 تهيئة خدمة التخزين الهجين...');

      // تحقق من الاتصال بالإنترنت ومزامنة البيانات
      await _syncData();

      if (kDebugMode) print('✅ تم تهيئة خدمة التخزين الهجين بنجاح');
    } catch (e) {
      if (kDebugMode)
        print('⚠️ تعذر الاتصال بالسحابة، سيتم استخدام التخزين المحلي: $e');
    }
  }

  /// مزامنة البيانات بين السحابة والتخزين المحلي
  static Future<void> _syncData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getInt(_lastSyncKey) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      // مزامنة كل 5 دقائق
      if (currentTime - lastSync > 300000) {
        await _syncToCloud();
        await _syncFromCloud();
        await prefs.setInt(_lastSyncKey, currentTime);

        if (kDebugMode) print('🔄 تمت مزامنة البيانات مع السحابة');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في المزامنة: $e');
    }
  }

  /// رفع البيانات المحلية للسحابة
  static Future<void> _syncToCloud() async {
    try {
      // مزامنة المنتجات
      final products = await DataService.getProducts();
      for (final product in products) {
        await _firestore
            .collection('products')
            .doc(product.id)
            .set(product.toJson(), SetOptions(merge: true));
      }

      // مزامنة الطلبات
      final orders = await DataService.getOrders();
      for (final order in orders) {
        await _firestore
            .collection('orders')
            .doc(order['id'])
            .set(order, SetOptions(merge: true));
      }

      // مزامنة المستخدمين
      final users = await DataService.getUsers();
      for (final user in users) {
        await _firestore
            .collection('users')
            .doc(user['id'])
            .set(user, SetOptions(merge: true));
      }

      // مزامنة الفئات
      final categories = await DataService.getCategories();
      for (final category in categories) {
        await _firestore
            .collection('categories')
            .doc(category.id)
            .set(category.toJson(), SetOptions(merge: true));
      }

      if (kDebugMode) print('⬆️ تم رفع البيانات للسحابة');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في رفع البيانات: $e');
    }
  }

  /// تحميل البيانات من السحابة
  static Future<void> _syncFromCloud() async {
    try {
      // تحميل المنتجات من السحابة
      final productsSnapshot = await _firestore.collection('products').get();
      for (final doc in productsSnapshot.docs) {
        final productData = doc.data();
        // تحويل البيانات إلى نموذج Product ثم حفظها
        // await DataService.addProduct(productData); // تعطيل مؤقت
      }

      // تحميل الطلبات من السحابة
      final ordersSnapshot = await _firestore.collection('orders').get();
      for (final doc in ordersSnapshot.docs) {
        final orderData = doc.data();
        await DataService.addOrder(orderData);
      }

      // تحميل المستخدمين من السحابة
      final usersSnapshot = await _firestore.collection('users').get();
      for (final doc in usersSnapshot.docs) {
        final userData = doc.data();
        await DataService.addUser(userData);
      }

      // تحميل الفئات من السحابة
      final categoriesSnapshot = await _firestore
          .collection('categories')
          .get();
      for (final doc in categoriesSnapshot.docs) {
        final categoryData = doc.data();
        // await DataService.addCategory(categoryData); // تعطيل مؤقت
      }

      if (kDebugMode) print('⬇️ تم تحميل البيانات من السحابة');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحميل البيانات: $e');
    }
  }

  /// إضافة منتج (سحابي ومحلي)
  static Future<void> addProduct(Map<String, dynamic> productData) async {
    try {
      // حفظ محلي أولاً
      // await DataService.addProduct(productData); // تعطيل مؤقت

      // محاولة حفظ سحابي
      try {
        await _firestore
            .collection('products')
            .doc(productData['id'])
            .set(productData, SetOptions(merge: true));

        if (kDebugMode) print('✅ تم حفظ المنتج في السحابة والتخزين المحلي');
      } catch (e) {
        if (kDebugMode) print('⚠️ تم حفظ المنتج محلياً فقط: $e');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حفظ المنتج: $e');
      rethrow;
    }
  }

  /// إضافة طلب (سحابي ومحلي)
  static Future<void> addOrder(Map<String, dynamic> orderData) async {
    try {
      // حفظ محلي أولاً
      await DataService.addOrder(orderData);

      // محاولة حفظ سحابي
      try {
        await _firestore
            .collection('orders')
            .doc(orderData['id'])
            .set(orderData, SetOptions(merge: true));

        if (kDebugMode) print('✅ تم حفظ الطلب في السحابة والتخزين المحلي');
      } catch (e) {
        if (kDebugMode) print('⚠️ تم حفظ الطلب محلياً فقط: $e');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حفظ الطلب: $e');
      rethrow;
    }
  }

  /// تحديث طلب (سحابي ومحلي)
  static Future<void> updateOrder(Map<String, dynamic> orderData) async {
    try {
      // تحديث محلي أولاً
      await DataService.updateOrder(orderData);

      // محاولة تحديث سحابي
      try {
        await _firestore
            .collection('orders')
            .doc(orderData['id'])
            .update(orderData);

        if (kDebugMode) print('✅ تم تحديث الطلب في السحابة والتخزين المحلي');
      } catch (e) {
        if (kDebugMode) print('⚠️ تم تحديث الطلب محلياً فقط: $e');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحديث الطلب: $e');
      rethrow;
    }
  }

  /// إضافة مستخدم (سحابي ومحلي)
  static Future<void> addUser(Map<String, dynamic> userData) async {
    try {
      // حفظ محلي أولاً
      await DataService.addUser(userData);

      // محاولة حفظ سحابي
      try {
        await _firestore
            .collection('users')
            .doc(userData['id'])
            .set(userData, SetOptions(merge: true));

        if (kDebugMode) print('✅ تم حفظ المستخدم في السحابة والتخزين المحلي');
      } catch (e) {
        if (kDebugMode) print('⚠️ تم حفظ المستخدم محلياً فقط: $e');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حفظ المستخدم: $e');
      rethrow;
    }
  }

  /// مزامنة يدوية
  static Future<void> manualSync() async {
    try {
      if (kDebugMode) print('🔄 بدء المزامنة اليدوية...');
      await _syncToCloud();
      await _syncFromCloud();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);

      if (kDebugMode) print('✅ تمت المزامنة اليدوية بنجاح');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في المزامنة اليدوية: $e');
      rethrow;
    }
  }

  /// التحقق من حالة الاتصال بالسحابة
  static Future<bool> isCloudConnected() async {
    try {
      await _firestore.collection('test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على آخر وقت مزامنة
  static Future<DateTime?> getLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastSyncKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
*/
