import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:convert';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';
import 'dart:js' as js;

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _skuController = TextEditingController();

  List<category_model.Category> _categories = [];
  String? _selectedCategoryId;
  bool _isLoading = false;
  bool _isActive = true;
  bool _isFeatured = false;

  // متغيرات الصور
  final ImagePicker _picker = ImagePicker();
  XFile? _mainImage;
  List<XFile> _additionalImages = [];
  final int _maxImages = 5;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _skuController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await ApiService.getCategories();
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      if (kDebugMode) print('خطأ في تحميل الفئات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إضافة منتج جديد'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildSectionCard(
                title: 'المعلومات الأساسية',
                children: [
                  _buildTextField(
                    controller: _nameController,
                    label: 'اسم المنتج',
                    hint: 'أدخل اسم المنتج',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'وصف المنتج',
                    hint: 'أدخل وصف تفصيلي للمنتج',
                    maxLines: 4,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _brandController,
                    label: 'العلامة التجارية',
                    hint: 'أدخل اسم العلامة التجارية',
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _skuController,
                    label: 'رمز المنتج (SKU)',
                    hint: 'أدخل رمز المنتج الفريد',
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // السعر والمخزون
              _buildSectionCard(
                title: 'السعر والمخزون',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: 'السعر (IQD)',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال السعر';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _stockController,
                          label: 'الكمية المتوفرة',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال الكمية';
                            }
                            if (int.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // الصور
              _buildSectionCard(
                title: 'صور المنتج',
                children: [_buildImageSection()],
              ),

              const SizedBox(height: 20),

              // الفئة والإعدادات
              _buildSectionCard(
                title: 'الفئة والإعدادات',
                children: [
                  _buildCategoryDropdown(),
                  const SizedBox(height: 16),
                  _buildSwitchTile(
                    title: 'منتج نشط',
                    subtitle: 'سيظهر المنتج للعملاء',
                    value: _isActive,
                    onChanged: (value) => setState(() => _isActive = value),
                  ),
                  _buildSwitchTile(
                    title: 'منتج مميز',
                    subtitle: 'سيظهر في قسم المنتجات المميزة',
                    value: _isFeatured,
                    onChanged: (value) => setState(() => _isFeatured = value),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading
                          ? null
                          : () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: AppColors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.white,
                                ),
                              ),
                            )
                          : const Text('حفظ المنتج'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCategoryId,
      decoration: const InputDecoration(
        labelText: 'الفئة',
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      hint: const Text('اختر فئة المنتج'),
      items: _categories.map((category) {
        return DropdownMenuItem(value: category.id, child: Text(category.name));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategoryId = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار فئة المنتج';
        }
        return null;
      },
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primaryColor,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // الصورة الرئيسية
        const Text(
          'الصورة الرئيسية',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        _buildMainImagePicker(),

        const SizedBox(height: 20),

        // الصور الإضافية
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'صور إضافية',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.primaryText,
              ),
            ),
            Text(
              '${_additionalImages.length}/$_maxImages',
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.secondaryText,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        _buildAdditionalImagesPicker(),
      ],
    );
  }

  Widget _buildMainImagePicker() {
    return GestureDetector(
      onTap: _pickMainImage,
      child: Container(
        width: double.infinity,
        height: 200,
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.grey.withValues(alpha: 0.3),
            width: 2,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(12),
          color: AppColors.grey.withValues(alpha: 0.1),
        ),
        child: _mainImage != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: kIsWeb
                    ? Image.network(
                        _mainImage!.path,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Icon(
                              Icons.error,
                              color: AppColors.errorColor,
                              size: 50,
                            ),
                          );
                        },
                      )
                    : Image.file(
                        File(_mainImage!.path),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Icon(
                              Icons.error,
                              color: AppColors.errorColor,
                              size: 50,
                            ),
                          );
                        },
                      ),
              )
            : const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_photo_alternate_outlined,
                    size: 50,
                    color: AppColors.grey,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'اضغط لإضافة الصورة الرئيسية',
                    style: TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'JPG, PNG (الحد الأقصى: 5MB)',
                    style: TextStyle(color: AppColors.grey, fontSize: 12),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildAdditionalImagesPicker() {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _additionalImages.length + 1,
        itemBuilder: (context, index) {
          if (index == _additionalImages.length) {
            // زر إضافة صورة جديدة
            return _buildAddImageButton();
          } else {
            // صورة موجودة
            return _buildImageItem(_additionalImages[index], index);
          }
        },
      ),
    );
  }

  Widget _buildAddImageButton() {
    if (_additionalImages.length >= _maxImages) {
      return const SizedBox.shrink();
    }

    return Container(
      width: 100,
      height: 100,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.primaryColor.withValues(alpha: 0.3),
          width: 2,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(8),
        color: AppColors.primaryColor.withValues(alpha: 0.1),
      ),
      child: InkWell(
        onTap: _pickAdditionalImage,
        borderRadius: BorderRadius.circular(8),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add, color: AppColors.primaryColor, size: 30),
            SizedBox(height: 4),
            Text(
              'إضافة',
              style: TextStyle(
                color: AppColors.primaryColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(XFile image, int index) {
    return Container(
      width: 100,
      height: 100,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.grey.withValues(alpha: 0.3)),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(7),
            child: kIsWeb
                ? Image.network(
                    image.path,
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 100,
                        height: 100,
                        color: AppColors.grey.withValues(alpha: 0.2),
                        child: const Icon(
                          Icons.error,
                          color: AppColors.errorColor,
                        ),
                      );
                    },
                  )
                : Image.file(
                    File(image.path),
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 100,
                        height: 100,
                        color: AppColors.grey.withValues(alpha: 0.2),
                        child: const Icon(
                          Icons.error,
                          color: AppColors.errorColor,
                        ),
                      );
                    },
                  ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeAdditionalImage(index),
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: AppColors.errorColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: AppColors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // دوال اختيار الصور
  Future<void> _pickMainImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _mainImage = image;
        });
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في اختيار الصورة الرئيسية: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('فشل في اختيار الصورة')));
      }
    }
  }

  Future<void> _pickAdditionalImage() async {
    if (_additionalImages.length >= _maxImages) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('الحد الأقصى للصور هو $_maxImages')),
      );
      return;
    }

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _additionalImages.add(image);
        });
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في اختيار الصورة الإضافية: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('فشل في اختيار الصورة')));
      }
    }
  }

  void _removeAdditionalImage(int index) {
    setState(() {
      _additionalImages.removeAt(index);
    });
  }

  // تحويل الصور إلى base64 للحفظ
  Future<String> _imageToBase64(XFile image) async {
    try {
      final bytes = await image.readAsBytes();
      return base64Encode(bytes);
    } catch (e) {
      if (kDebugMode) print('خطأ في تحويل الصورة: $e');
      return '';
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // البحث عن اسم الفئة
      final selectedCategory = _categories.firstWhere(
        (cat) => cat.id == _selectedCategoryId,
        orElse: () => _categories.first,
      );

      // تحويل الصور إلى base64
      String mainImageBase64 = '';
      List<String> additionalImagesBase64 = [];

      if (_mainImage != null) {
        mainImageBase64 = await _imageToBase64(_mainImage!);
      }

      for (XFile image in _additionalImages) {
        final imageBase64 = await _imageToBase64(image);
        if (imageBase64.isNotEmpty) {
          additionalImagesBase64.add(imageBase64);
        }
      }

      // إنشاء منتج جديد
      final newProduct = Product(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: double.parse(_priceController.text),
        image: mainImageBase64, // الصورة الرئيسية
        images: additionalImagesBase64, // الصور الإضافية
        categoryId: _selectedCategoryId!,
        categoryName: selectedCategory.name,
        rating: 0.0,
        reviewsCount: 0,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        specifications: {},
        brand: _brandController.text.trim().isEmpty
            ? null
            : _brandController.text.trim(),
        type: ProductType.eyeglasses, // افتراضي، يمكن تحسينه لاحقاً
        stock: int.parse(_stockController.text),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isFeatured: _isFeatured,
        tags: _skuController.text.trim().isEmpty
            ? null
            : _skuController.text.trim(),
      );

      // محاكاة حفظ المنتج
      await Future.delayed(const Duration(seconds: 1));

      // حفظ المنتج في Firestore والبيانات المحلية
      try {
        await FirestoreDataService.addProduct(newProduct);
      } catch (e) {
        if (kDebugMode) print('خطأ في حفظ المنتج في Firestore: $e');
        // في حالة فشل Firestore، احفظ محلياً
        await ApiService.addProduct(newProduct);
      }
      if (kDebugMode) print('تم إنشاء منتج جديد: ${newProduct.name}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المنتج بنجاح - سيتم إعادة تحميل الصفحة'),
            backgroundColor: AppColors.successColor,
            duration: Duration(seconds: 2),
          ),
        );

        // انتظار قصير ثم إعادة تحميل الصفحة لجلب البيانات المحدثة
        await Future.delayed(const Duration(seconds: 2));

        if (kIsWeb) {
          // إعادة تحميل الصفحة في الويب
          try {
            js.context.callMethod('location.reload');
          } catch (reloadError) {
            // تجاهل خطأ إعادة التحميل - هذا طبيعي
            if (kDebugMode) print('إعادة تحميل الصفحة: $reloadError');
          }
        } else {
          if (mounted) {
            Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح الإضافة
          }
        }
      }
    } catch (e) {
      if (kDebugMode) print('خطأ في حفظ المنتج: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ المنتج'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
