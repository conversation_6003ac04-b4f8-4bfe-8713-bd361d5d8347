import 'package:flutter/foundation.dart';
import 'dart:js' as js;
import 'dart:convert';
import '../models/product.dart';
import '../models/user_simple.dart' as user_model;

/// خدمة Firebase للويب باستخدام JavaScript API
class FirebaseWebService {
  static bool _isInitialized = false;

  /// طباعة رسالة في Console
  static void _logMessage(String message) {
    print('🌐 Firebase Web: $message');
    if (kDebugMode) {
      debugPrint('🌐 Firebase Web: $message');
    }
  }

  /// تهيئة الخدمة
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        _logMessage('Firebase Web مُهيأ مسبقاً');
        return true;
      }

      _logMessage('🔥 بدء تهيئة Firebase Web Service...');

      // التحقق من وجود Firebase و firebaseHelpers في JavaScript
      if (js.context.hasProperty('firebase') &&
          js.context.hasProperty('firebaseHelpers')) {
        _logMessage('✅ Firebase و firebaseHelpers متاحان في JavaScript');
        _isInitialized = true;
        return true;
      } else {
        _logMessage('❌ Firebase أو firebaseHelpers غير متاح في JavaScript');
        return false;
      }
    } catch (e) {
      _logMessage('❌ خطأ في تهيئة Firebase Web Service: $e');
      return false;
    }
  }

  /// اختبار الاتصال بـ Firestore
  static Future<void> _testConnection() async {
    try {
      _logMessage('🔄 اختبار الاتصال بـ Firestore...');

      // استدعاء JavaScript لاختبار Firestore
      final result = js.context.callMethod('eval', [
        '''
        (async function() {
          try {
            const db = firebase.firestore();
            const snapshot = await db.collection('test').limit(1).get();
            return { success: true, size: snapshot.size };
          } catch (error) {
            return { success: false, error: error.message };
          }
        })()
      ''',
      ]);

      _logMessage('✅ اختبار الاتصال بـ Firestore نجح');
    } catch (e) {
      _logMessage('❌ فشل اختبار الاتصال بـ Firestore: $e');
    }
  }

  /// جلب المنتجات من Firestore
  static Future<List<Product>> getProducts() async {
    try {
      _logMessage('🔄 جلب المنتجات من Firestore...');

      if (!_isInitialized) {
        await initialize();
      }

      // التحقق من وجود firebaseHelpers
      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return [];
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      try {
        // أولاً: جلب البيانات من Firestore
        firebaseHelpers.callMethod('getProducts');

        // انتظار قصير للمعالجة
        await Future.delayed(const Duration(milliseconds: 1000));

        // ثانياً: جلب البيانات المحفوظة
        final cachedProducts = firebaseHelpers.callMethod('getCachedProducts');

        if (cachedProducts != null) {
          _logMessage('📦 تم جلب البيانات المحفوظة من JavaScript');

          // تحويل البيانات إلى قائمة منتجات
          final List<Product> products = [];

          if (cachedProducts is List) {
            for (var item in cachedProducts) {
              try {
                if (item is Map) {
                  final productMap = Map<String, dynamic>.from(item);
                  products.add(Product.fromJson(productMap));
                }
              } catch (e) {
                _logMessage('❌ خطأ في تحويل منتج: $e');
              }
            }
          }

          _logMessage('✅ تم تحويل ${products.length} منتج بنجاح');
          return products;
        }
      } catch (e) {
        _logMessage('❌ خطأ في استدعاء JavaScript: $e');
      }

      return [];
    } catch (e) {
      _logMessage('❌ خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// إضافة منتج إلى Firestore
  static Future<bool> addProduct(Product product) async {
    try {
      _logMessage('🔄 إضافة منتج: ${product.name}');

      if (!_isInitialized) {
        await initialize();
      }

      // التحقق من وجود firebaseHelpers
      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      // تحويل المنتج إلى Map
      final productData = product.toJson();

      // تحويل إلى JavaScript Object
      final jsProductData = js.JsObject.jsify(productData);

      // استدعاء دالة JavaScript
      final firebaseHelpers = js.context['firebaseHelpers'];
      firebaseHelpers.callMethod('addProduct', [jsProductData]);

      // انتظار قصير للمعالجة
      await Future.delayed(const Duration(milliseconds: 500));

      _logMessage('✅ تم إضافة المنتج بنجاح: ${product.name}');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تسجيل الدخول بـ Google
  static Future<user_model.User?> signInWithGoogle() async {
    try {
      _logMessage('🔄 بدء تسجيل الدخول بـ Google...');

      if (!_isInitialized) {
        await initialize();
      }

      // استدعاء JavaScript لتسجيل الدخول بـ Google
      js.context.callMethod('eval', [
        '''
        (async function() {
          try {
            const provider = new firebase.auth.GoogleAuthProvider();
            const result = await firebase.auth().signInWithPopup(provider);
            const user = result.user;
            return {
              success: true,
              user: {
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                photoURL: user.photoURL
              }
            };
          } catch (error) {
            return { success: false, error: error.message };
          }
        })()
      ''',
      ]);

      _logMessage('✅ تم تسجيل الدخول بـ Google بنجاح');
      return null; // سنحسن هذا لاحقاً
    } catch (e) {
      _logMessage('❌ خطأ في تسجيل الدخول بـ Google: $e');
      return null;
    }
  }

  /// تسجيل الخروج
  static Future<void> signOut() async {
    try {
      _logMessage('🔄 تسجيل الخروج...');

      if (!_isInitialized) {
        await initialize();
      }

      // استدعاء JavaScript لتسجيل الخروج
      js.context.callMethod('eval', [
        '''
        firebase.auth().signOut();
      ''',
      ]);

      _logMessage('✅ تم تسجيل الخروج بنجاح');
    } catch (e) {
      _logMessage('❌ خطأ في تسجيل الخروج: $e');
    }
  }

  /// التحقق من حالة تسجيل الدخول
  static bool isSignedIn() {
    try {
      if (!_isInitialized) return false;

      final result = js.context.callMethod('eval', [
        '''
        firebase.auth().currentUser !== null
      ''',
      ]);

      return result == true;
    } catch (e) {
      _logMessage('❌ خطأ في التحقق من حالة تسجيل الدخول: $e');
      return false;
    }
  }
}
