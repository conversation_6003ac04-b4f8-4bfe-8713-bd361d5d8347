/// نموذج منتج مبسط للاستخدام مع Firebase Web
class SimpleProduct {
  final String id;
  final String name;
  final String description;
  final double price;
  final int stock;
  final String categoryId;
  final String categoryName;
  final String brand;
  final String type;
  final String image;
  final bool inStock;
  final bool isFeatured;
  final bool isNew;
  final bool isOnSale;
  final double rating;
  final int reviewsCount;

  SimpleProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.stock,
    required this.categoryId,
    this.categoryName = '',
    this.brand = '',
    this.type = '',
    this.image = '',
    this.inStock = true,
    this.isFeatured = false,
    this.isNew = false,
    this.isOnSale = false,
    this.rating = 0.0,
    this.reviewsCount = 0,
  });

  /// إنشاء منتج من JSON مع معالجة آمنة للأخطاء
  factory SimpleProduct.fromJson(Map<String, dynamic> json) {
    try {
      return SimpleProduct(
        id: _safeString(json['id']),
        name: _safeString(json['name']),
        description: _safeString(json['description']),
        price: _safeDouble(json['price']),
        stock: _safeInt(json['stock']) ?? _safeInt(json['stockQuantity']) ?? 0,
        categoryId: _safeString(json['categoryId']),
        categoryName: _safeString(json['categoryName']),
        brand: _safeString(json['brand']),
        type: _safeString(json['type']),
        image: _safeString(json['image']),
        inStock: _safeBool(json['inStock']),
        isFeatured: _safeBool(json['isFeatured']),
        isNew: _safeBool(json['isNew']),
        isOnSale: _safeBool(json['isOnSale']),
        rating: _safeDouble(json['rating']),
        reviewsCount: _safeInt(json['reviewsCount']),
      );
    } catch (e) {
      print('❌ خطأ في تحويل SimpleProduct: $e');
      print('📋 البيانات: $json');
      rethrow;
    }
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'stock': stock,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'brand': brand,
      'type': type,
      'image': image,
      'inStock': inStock,
      'isFeatured': isFeatured,
      'isNew': isNew,
      'isOnSale': isOnSale,
      'rating': rating,
      'reviewsCount': reviewsCount,
    };
  }

  /// دوال مساعدة آمنة لتحويل البيانات
  static String _safeString(dynamic value) {
    if (value == null) return '';
    return value.toString();
  }

  static double _safeDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  static int _safeInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  static bool _safeBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    if (value is int) return value != 0;
    return false;
  }

  @override
  String toString() {
    return 'SimpleProduct(id: $id, name: $name, price: $price)';
  }
}
